@import '../../../../public/variables.css';

.groupAutocomplete {
  input {
    color: var(--divi-dark-purp);
  }
  label {
    color: var(--divi-light-purp);
  }
  fieldset {
    border: 2px solid var(--divi-light-purp);
  }
}

.chatSettingsLines {
  padding: 12px;
    margin: 10px;
    border: 1px solid rgb(105 116 140 / 19%);
    border-radius: 5px;
}

.inviteeEmail {
  display: flex;
  align-items: center;
  gap: 23px;
}

.inviteLinkInput {
  width: 100% !important;
}

.inviteeEmailLinkBox {
  display: flex;
  gap: 22px;
}

@media (max-width: 768px) {
  .inviteeEmailLinkBox {
    display: block;
    padding: 1px;
  }
}