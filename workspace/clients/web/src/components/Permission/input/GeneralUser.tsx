import React from "react";
import { resolveUrl } from "@divinci-ai/utils";

import { useDocPermission } from "../data/DocPermissionContext";

import { AsyncUpdateRestrictedEnumInput } from "./RestrictedEnumInput";


export function GeneralUserInput({ apiUrlPrefix }: { apiUrlPrefix: string }){

  const { value, update } = useDocPermission();

  if(!value){
    return null;
  }

  const { doc, allPermissions } = value;

  return (
    <div>
      <div>
        <h1>Logged In Permissions</h1>
        <AsyncUpdateRestrictedEnumInput
          url={resolveUrl(apiUrlPrefix, "loggedin")}
          prepareNewValue={(permissions)=>({ permissions })}
          value={doc.loggedInPermissions.permissions}
          onChange={()=>(update())}
          descriptions={allPermissions}
          restriction={doc.loggedInRestriction}
        />
      </div>
      <div>
        <h1>Anonymous Permissions</h1>
        <AsyncUpdateRestrictedEnumInput
          url={resolveUrl(apiUrlPrefix, "anonymous")}
          prepareNewValue={(permissions)=>({ permissions })}
          value={doc.anonymousPermissions.permissions}
          onChange={()=>(update())}
          descriptions={allPermissions}
          restriction={doc.anonymousRestriction}
        />
      </div>
    </div>
  );
}

