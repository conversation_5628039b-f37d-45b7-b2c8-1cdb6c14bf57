import React from "react";
import { ChangeOwnerInput } from "./ChangeOwner";
import { GeneralUserInput } from "./GeneralUser";
import {
  InviteUserForm,
  InviteUsersInput,
  ActiveUsersInput,
} from "./IndividualUser";
import { UserGroup } from "./UserGroup";

import { DocPermissionContextProvider } from "../data/DocPermissionContext";

export function DocPermissionInput({ apiUrlPrefix }: { apiUrlPrefix: string }){
  if(apiUrlPrefix.charAt(-1) !== "/"){
    apiUrlPrefix += "/";
  }

  return (
    <div className="doc-permissions-container container">
      <div className="section">
        <h1>Settings</h1>
        <hr/>
        <DocPermissionContextProvider apiUrlPrefix={apiUrlPrefix}>
          <div className="container is-fluid">
            <div className="columns is-centered box bg-gradient-gradient_dark">
              <div className="column is-three-quarters">
                <section className="box">
                  <h1 className="title is-4">👤 Invite Users</h1>
                  <InviteUserForm apiUrlPrefix={apiUrlPrefix} />
                  <InviteUsersInput apiUrlPrefix={apiUrlPrefix} />
                </section>

                <section className="box">
                  <h1 className="title is-4">👤 Active Users</h1>
                  <ActiveUsersInput apiUrlPrefix={apiUrlPrefix} />
                </section>

                <section className="box">
                  <h1 className="title is-4">👥 Allowed Organizations</h1>
                  <UserGroup apiUrlPrefix={apiUrlPrefix} />
                </section>

                <section className="box">
                  <h1 className="title is-4">👤 Change Owner</h1>
                  <ChangeOwnerInput apiUrlPrefix={apiUrlPrefix} />
                </section>

                <section className="box">
                  <h1 className="title is-4">🔐 Default Permissions</h1>
                  <GeneralUserInput apiUrlPrefix={apiUrlPrefix} />
                </section>
              </div>
            </div>
          </div>
        </DocPermissionContextProvider>
      </div>
    </div>
  );
}
