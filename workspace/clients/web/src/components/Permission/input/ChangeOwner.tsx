import React, { useState } from "react";

import { useDocPermission } from "../data/DocPermissionContext";
import { useFetcher } from "../../../util/react/fetcher";
import { InvitationLink } from "./InvitationLink";
import { resolveUrl } from "@divinci-ai/utils";

export function ChangeOwnerInput({ apiUrlPrefix }: { apiUrlPrefix: string }) {
  const { value, update } = useDocPermission();

  if (!value) {
    return null;
  }

  const { doc } = value;

  if (doc.invitedOwner) {
    return (
      <NewOwnerExists
        apiUrlPrefix={apiUrlPrefix}
        update={update}
        email={doc.invitedOwner}
      />
    );
  }
  return <SetNewOwner apiUrlPrefix={apiUrlPrefix} update={update} />;
}

function NewOwnerExists({
  email,
  apiUrlPrefix,
  update,
}: {
  email: string;
  apiUrlPrefix: string;
  update: () => any;
}) {
  const fetcher = useFetcher();

  return (
    <>
      <div>
        <span>New Owner Email: </span>
        <span>{email}</span>
        <button
          className="button"
          onClick={async () => {
            await fetcher(resolveUrl(apiUrlPrefix, "new-owner"), {
              method: "DELETE",
            });
            update();
          }}
        >
          Delete New Owner
        </button>
      </div>
      <div>
        <span>Invite Url:</span>
        <InvitationLink />
      </div>
    </>
  );
}

function SetNewOwner({
  apiUrlPrefix,
  update,
}: {
  apiUrlPrefix: string;
  update: () => any;
}) {
  const fetcher = useFetcher();
  const [email, setEmail] = useState("");
  return (
    <div>
      <input
        className="input"
        type="text"
        placeholder="New Owner Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <button
        className="button"
        onClick={async () => {
          await fetcher(resolveUrl(apiUrlPrefix, "new-owner"), {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ email }),
          });
          update();
        }}
      >
        Invite New Owner
      </button>
    </div>
  );
}
