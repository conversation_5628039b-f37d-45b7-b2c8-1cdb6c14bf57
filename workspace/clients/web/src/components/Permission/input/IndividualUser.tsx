import React, { useState } from "react";
import { useDocPermission } from "../data/DocPermissionContext";

import {
  AsyncUpdateRestrictedEnumInput,
  RestrictedEnumInput,
} from "./RestrictedEnumInput";
import { InvitationLink } from "./InvitationLink";
import { resolveUrl } from "@divinci-ai/utils";
import { useFetcher } from "../../../util/react/fetcher";
import { extractErrorString } from "../../../util/error";
import styles from "./usergroup.module.css";

export function InviteUserForm({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const fetcher = useFetcher();
  const { value, update } = useDocPermission();
  const [error, setError] = useState<undefined | string>();
  const [email, setEmail] = useState("");
  const [permissions, setPermissions] = useState<Array<string>>([]);

  if(!value){
    return null;
  }

  const { doc, allPermissions } = value;

  return (
    <div className="invite-user-form box">
      <div>
        <button
          className="button"
          onClick={async ()=>{
            try {
              await fetcher(resolveUrl(apiUrlPrefix, "invite-user"), {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ email, permissions }),
              });
              update();
            }catch(e: any){
              console.error(e);
              const str = extractErrorString(e);
              setError(str);
            }
          }}
        >
          <strong>Add User</strong>
        </button>
        <input
          className="input"
          placeholder="Email"
          type="email"
          value={email}
          onChange={(e)=>(setEmail(e.target.value))}
        />
      </div>
      {!error ? null : <div>{error}</div>}
      <RestrictedEnumInput
        value={permissions}
        onChange={setPermissions}
        restriction={doc.userRestriction}
        descriptions={allPermissions}
      />
    </div>
  );
}

export function InviteUsersInput({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const fetcher = useFetcher();
  const { value, update } = useDocPermission();

  if(!value){
    return null;
  }

  const { doc, allPermissions } = value;

  const map = doc.userInvitations || {};

  if(Object.keys(map).length === 0){
    return null;
  }

  return (
    <div className="existing-invites">
      <h1 className="title is-4">👤 Existing Invites</h1>
      <ul className="invitees box">
        {Object.entries(map).map(([key, item])=>(
          <li key={key}>
            <div className={`${styles.inviteeEmailLinkBox} box`}>
              <div className={`${styles.inviteeEmail}`}>
                <span><strong>👤 Email: </strong></span>
                <span className="email"><strong>{item.email}</strong></span>
              </div>
              <div className="invitation-url">
                <InvitationLink />
              </div>
              <button
                className="button"
                onClick={async ()=>{
                  await fetcher(resolveUrl(apiUrlPrefix, "invite-user"), {
                    method: "DELETE",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ email: item.email }),
                  });
                  update();
                }}
              >
                Delete Invitation
              </button>
            </div>
            <div className="box">
              <strong className="strong users-group-permissions">
                  {item.email}'s Group Permissions
              </strong>
              <AsyncUpdateRestrictedEnumInput
                url={resolveUrl(apiUrlPrefix, "invite-user")}
                value={item.permissions}
                prepareNewValue={(permissions)=>({
                  email: item.email,
                  permissions,
                })}
                onChange={()=>(update())}
                descriptions={allPermissions}
                restriction={doc.userRestriction}
              />
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}

export function ActiveUsersInput({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const fetcher = useFetcher();
  const { loading, value, update } = useDocPermission();

  if(loading) return null;

  const noActiveUsersMessage = (
    <div className="block">
      <h4>No active users besides your beautiful self!</h4>
    </div>
  );

  if(!value){
    return (
      <div className="block">
        <h4>There was an issue loading user permissions. Please try again later. Thank you.</h4>
      </div>
    );
  }

  const { doc, allPermissions } = value;
  const map = doc.userPermissions || {};

  if(Object.keys(map).length === 0){
    return noActiveUsersMessage;
  }

  return (
    <ul>
      {Object.entries(map).map(([key, item])=>(
        <li key={key}>
            <div className={`${styles.inviteeEmailLinkBox} box`}>
              <div className={`${styles.inviteeEmail}`}>
                <span><strong>👤 Email: </strong></span>
                <input
                  className="input"
                  type="email"
                  value={item.email}
                  readOnly
                />
              </div>
            <button
              className="button is-danger"
              onClick={async ()=>{
                await fetcher(resolveUrl(apiUrlPrefix, "active-user"), {
                  method: "DELETE",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({ userId: item.userId }),
                });
                update();
              }}
            >
              Delete User
            </button>
          </div>
          <AsyncUpdateRestrictedEnumInput
            url={resolveUrl(apiUrlPrefix, "active-user")}
            value={item.permissions}
            prepareNewValue={(permissions)=>({
              userId: item.userId,
              permissions,
            })}
            onChange={()=>(update())}
            descriptions={allPermissions}
            restriction={doc.userRestriction}
          />
        </li>
      ))}
    </ul>
  );
}
