import React, { useEffect, useState, useCallback } from "react";
import { Link } from "react-router-dom";
import { PATH_USERGROUP_INDEX } from "../../../pages/UserGroup/paths";
import {
  AsyncUpdateRestrictedEnumInput,
  RestrictedEnumInput,
} from "./RestrictedEnumInput";
import { useDocPermission } from "../data/DocPermissionContext";
import { resolveUrl } from "@divinci-ai/utils";
import { useFetcher } from "../../../util/react/fetcher";
import { extractErrorString } from "../../../util/error";

import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import { useTrackedValue } from "../../../util/react/tracked-value";
import { UserGroup as UserGroupModel } from "@divinci-ai/models";
import { VerifiedEmail } from "../../../pages/WhiteLabel/Setup/Notifications/email";
import { DefaultNotificationSettingsWrapper } from "../../../pages/WhiteLabel/Setup/Notifications/DefaultNotificationSettings";
import styles from "./usergroup.module.css";

export function UserGroup({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const [defaultEmails, setDefaultEmails] = useState<VerifiedEmail[]>([]);

  // Use useCallback to memoize the function and prevent re-renders
  const updateDefaultNotificationEmails = useCallback(
    (newNotificationData: { emails: VerifiedEmail[] })=>{
      setDefaultEmails(newNotificationData.emails);
    },
    [],
  );

  return (
    <div className="container">
      <div className="columns">
        <div className="column is-full">
          <section className="box">
            <GroupList apiUrlPrefix={apiUrlPrefix} />
          </section>
        </div>
      </div>

      <div className="columns">
        <div className="column is-full">
          <section className="box">
            <h1 className="title is-4">👥 Add Organization</h1>
            <GroupInput apiUrlPrefix={apiUrlPrefix} />
          </section>
        </div>
      </div>

      <div className="columns" style={{ marginTop: "2rem" }}>
        <div className="column is-full">
          <DefaultNotificationSettingsWrapper
            emails={defaultEmails}
            updateDefaultNotificationEmails={updateDefaultNotificationEmails}
          />
        </div>
      </div>
    </div>
  );
}

function GroupInput({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const fetcher = useFetcher();
  const { value, update } = useDocPermission();
  const [error, setError] = useState<undefined | string>();
  const [groupSlug, setGroupSlug] = useState("");
  const [permissions, setPermissions] = useState<Array<string>>([]);

  const { value: ownGroups } = useTrackedValue<Array<UserGroupModel>>({
    url: "/user-group/own-groups",
  });

  useEffect(()=>{
    if(groupSlug === "") return;
    if(typeof value === "undefined") return;
    const { doc } = value;
    const groupConfig = doc.group[groupSlug];
    if(typeof groupConfig === "undefined") return;
    setPermissions(groupConfig.permissions);
  }, [groupSlug, value]);

  if(!value){
    return null;
  }

  const { doc, allPermissions } = value;

  return (
    <form
      className="form"
      onSubmit={async (e)=>{
        e.preventDefault();
        try {
          await fetcher(resolveUrl(apiUrlPrefix, "user-group"), {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ groupSlug, permissions }),
          });
          update();
        }catch(e: any){
          console.error(e);
          const str = extractErrorString(e);
          setError(str);
        }
      }}
    >
      <div className="field">
        <label className="label">Organization Name</label>
        <div className="control">
          <Autocomplete
            className={styles.groupAutocomplete}
            freeSolo
            options={(ownGroups || []).map((userGroup)=>userGroup.slug)}
            renderInput={(params)=>(
              <TextField {...params} label="Search for an organization" />
            )}
            value={groupSlug}
            onChange={(event, newValue)=>setGroupSlug(newValue || "")}
          />
        </div>
      </div>

      {!error ? null : <div className="notification is-danger">{error}</div>}

      <RestrictedEnumInput
        value={permissions}
        onChange={setPermissions}
        restriction={doc.userRestriction}
        descriptions={allPermissions}
      />

      <div className="field">
        <div className="control">
          <button className="button is-link" type="submit">
            <strong>Add Group</strong>
          </button>
        </div>
      </div>
    </form>
  );
}

function GroupList({ apiUrlPrefix }: { apiUrlPrefix: string }){
  const fetcher = useFetcher();
  const { value, update } = useDocPermission();

  if(!value){
    return null;
  }

  const { doc, allPermissions } = value;

  return (
    <ul className="list">
      {Object.values(doc.group).length > 0 ? (
        Object.values(doc.group).map((group)=>(
          <li key={group.groupSlug} className="box">
            <div className="level">
              <div className="level-left">
                <span className="title is-6">{group.groupSlug}</span>
              </div>
              <div className="level-right">
                <button
                  className="button is-danger"
                  onClick={async ()=>{
                    await fetcher(resolveUrl(apiUrlPrefix, "user-group"), {
                      method: "DELETE",
                      headers: { "Content-Type": "application/json" },
                      body: JSON.stringify({ groupSlug: group.groupSlug }),
                    });
                    update();
                  }}
                >
                  Delete Group Permissions
                </button>
              </div>
            </div>

            <AsyncUpdateRestrictedEnumInput
              url={resolveUrl(apiUrlPrefix, "user-group")}
              value={group.permissions}
              prepareNewValue={(permissions)=>({
                groupSlug: group.groupSlug,
                permissions,
              })}
              onChange={()=>update()}
              descriptions={allPermissions}
              restriction={doc.userRestriction}
            />
          </li>
        ))
      ) : (
        <ul>
          Add an existing organization below or create one{" "}
          <Link className="clickable-text" to={PATH_USERGROUP_INDEX}>here</Link>.
        </ul>
        )}
      </ul>
    );
  }
