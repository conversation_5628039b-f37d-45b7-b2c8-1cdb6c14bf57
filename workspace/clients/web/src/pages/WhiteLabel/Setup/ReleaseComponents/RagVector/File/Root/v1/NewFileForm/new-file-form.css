.rag-file-form {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    width: 100%;
}

.rag-file-form .box .label,
.chunking-tool-label {
    color: #363636;
    display: block;
    font-weight: 700;
    margin-bottom: 0.5em;
}

/* For dark mode */
@media (prefers-color-scheme: dark) {
    .rag-file-form .box .label,
    .chunking-tool-label {
        color: #ffffff;
    }
}

.text-area, .input {
    width: auto;
}

.box {
    margin: 11px;
    height: auto;
    background-color: #100e179e;
}

input[type="checkbox"] {
    margin-right: 8px; 
    margin-left: 5px;
}

input[type="text"] {
  margin-right: 8px; 
}

/* Needs light & dark mode */
.subtitle {
    font-weight: bold;
    font-size: x-large;
}

.hidden-box {
    flex-grow: 1;
    visibility: hidden;
    height: 0;
    width: 0;
    padding: 0;
    margin: 0;
}

.file-upload-and-chunking-tool {
    transition: all 0.3s ease;
    overflow: hidden;
}

.file-upload-and-chunking-tool.is-open {
    max-height: none;
    opacity: 1;
}

.file-upload-and-chunking-tool:not(.is-open) {
    max-height: 0;
    opacity: 0;
}

/* Styles for error notification */
.notification.is-danger.mt-4 {
    margin-top: 1rem;
}

/* Added styles moved from index.tsx */
.chunking-tool-label {
    color: #363636 !important;
}

@media (prefers-color-scheme: dark) {
    .chunking-tool-label {
        color: #ffffff !important;
    }
}

