import React, { useState } from "react";
import { Link, useParams } from "react-router-dom";
import { useTrackedValue } from "../../../util/react/tracked-value";
import { WhiteLabelReleaseDoc } from "@divinci-ai/models";
import { capitalizeFirst } from "@divinci-ai/utils";
import { replaceParams } from "../../../util/router";
import styles from "./whitelabelrelease.module.css";

import { PATH_WHITELABEL_RELEASE_ITEM } from "../paths";

import { AddReleaseToPreferences } from "./AddReleaseToPreferences";
import { UseReleaseForm } from "./UseReleaseForm";
import { OtherVersions } from "./OtherVersions";
import { SameAuthor } from "./SameAuthor";

export function WhitelabelReleaseItem(){
  const params = useParams();

  const { value: release } = useTrackedValue<WhiteLabelReleaseDoc>({ url: `/white-label-release/${params.releaseId}` });

  if(!release){
    return null;
  }

  return (
    <div className={`container ${styles.whitelabelRelaseContainer}`} >
      <div className="columns" >
        <div className="column is-half is-offset-one-quarter">
          <section className="box" >
            <h1>
              <Link className="clickable-text" to={replaceParams(PATH_WHITELABEL_RELEASE_ITEM, { releaseId: release._id })}>
                {release.title}
              </Link>
            </h1>
            <div>
              <strong>Version: </strong>
              <span>{`${release.slug}/${release.version}`}</span>
            </div>
            <div>
              {release.status === "deprecated" ? "No longer available to use" : (
                <>
                  <AddReleaseToPreferences />
                  <UseReleaseForm />
                </>
              )}
            </div>
            <div>
              <strong>Category: </strong>
              <span>{capitalizeFirst(release.category)}</span>
            </div>
            <div>
              <strong>Description: </strong>
              <span>{release.description}</span>
            </div>
            <div className={`${styles.titleStyles}`}>
              <h1 className="title">Other Versions</h1>
              <OtherVersions />
            </div>
            <div className={`${styles.titleStyles}`}>
              <h1 className="title">Same Author</h1>
              <SameAuthor />
            </div>
          </section>
      <ReleaseTabs />
    </div>
    </div>
    </div>
  );
}

import { ChatsUsingRelease } from "./ChatsUsingRelease";
import { ChatsNotUsingRelease } from "./ChatsNotUsingRelease";

const RELEASE_TABS = [
  { title: "Chats Using Release", component: ChatsUsingRelease },
  { title: "Not Using Release", component: ChatsNotUsingRelease },
];

function ReleaseTabs(){
  const [currentPage, setCurrentPage] = useState(RELEASE_TABS[0]);
  const Component = currentPage.component;
  return (
    <div>
        <div className="header-tabs tabs is-centered">
          <ul>
            {RELEASE_TABS.map((page)=>(
              <li key={page.title} className={page.title !== currentPage.title ? "" : "is-active"}>
                <a onClick={(e)=>{e.preventDefault(); setCurrentPage(page);}}>{page.title}</a>
              </li>
            ))}
          </ul>
        </div>
        <Component />
    </div>
  );
}
