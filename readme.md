# Local Divinci Web App

[![Deploy to Google Cloud Run](https://github.com/Divinci-AI/server/actions/workflows/build-deploy-changed-services.yml/badge.svg)](https://github.com/Divinci-AI/server/actions/workflows/build-deploy-changed-services.yml)
[![API Tests](https://github.com/Divinci-AI/server/actions/workflows/api-tests.yml/badge.svg)](https://github.com/Divinci-AI/server/actions/workflows/api-tests.yml)

### Preperation
```

# Cloning the server and all the submodules
<NAME_EMAIL>:Divinci-AI/server.git server
cd server

# Run the Onboarding Script:
./workspace/onboard.sh ../server

  ## Or manually install/setup everything:

  ###Installing Private Packages
  npm login --registry=https://npm.pkg.github.com

  #### Install the private keys
  git submodule sync
  git submodule update --init --recursive --remote

  ### Setup git hooks (husky)
  bash scripts/setup-git-hooks.sh

# Install pnpm on POISX system and then install packages
curl -fsSL https://get.pnpm.io/install.sh | sh -
pnpm install

```

_ _ _


### Running the Server

#### For normal local development
```
docker compose -f docker/local.yml up
```

_ _ _

#### Deploy to Gooogle Cloud

<!-- 1. Docker login
``` cmd
echo <GITHUB_TOKEN> | docker login ghcr.io -u <GITHUB_USERNAME> --password-stdin
```
THIS IS FOR GITHUB CONTAIN REGISTRY WHICH WE DONT SEEM TO BE USING ANY LONGER -->
1. Build a Docker image
``` cmd
docker build -t gcr.io/openai-api-4375643/divinci-web-client -f ./docker/ci/Dockerfile.web-client .
```
2. Push image to Google Cloud Container Registry
    1. Make sure you have `gcloud` CLI installed and if not:
       [https://cloud.google.com/sdk/docs/install-sdk](https://cloud.google.com/sdk/docs/install-sdk)
    2. Run the following command:
        ```cmd
        docker push gcr.io/openai-api-4375643/divinci-web-client
        ```
3. Deploy to Google Cloud Functions:
``` cmd
# Set project
gcloud config set project openai-api-4375643

# Deploy to Cloud Run
gcloud run deploy divinci-web-client --image gcr.io/openai-api-4375643/divinci-web-client --platform managed --region us-west1 --port 8080

```

Or

### To build and deploy all containers

```cmd
bash build-deploy-fn.sh
```

### To build and deploy only a single container
```bash
bash build-deploy-fn.sh web-client # for the web client
bash build-deploy-fn.sh api # for the api server
bash build-deploy-fn.sh api-live # for the api live server
bash build-deploy-fn.sh api-webhook # for the api webhook server
```


_ _ _


### Mongo Express (view our mongoDB databases/tables)

See `docker/local.yml`
localhost:8091:8081  # This line maps port 27017 in the container to port 27017 on your host machine


_ _ _

### Testing

- Running Jest Unit Tests:
`./scripts/run-jest-tests.sh` (optionally include a folder name to test a specific folder, i.e.: `./workspace/clients/web`)

- Running End-To-End (E2E) Tests:
`./scripts/e2e-docker-compose.sh local`

_ _ _

### Notes
- Please do not make the private keys repo public.
- When pushing code to GitHub use "git push --no-verify" command to make sure GitHub workflow tests are not run
- `docker compose` will create a folder at `./hidden/mongodb` and `./hidden/redis` for mongodb and redis to store their data.
  - This will persist between server restarts.
  - If you add services that you want to have their data persist locally, feel free to add a volume that points to a `./hidden/` subdirectory:
    - `./hidden/${my_service_name}` would be fine.
- Suggested Ignored Files/Folders when searching the mono-repo:
  - `node_modules, *hidden*, package-lock*, .txt, dist, *.js, *.test.*, *.md, Dockerfile.*, *.yml, env, *.env, text-router, *.html, pnpm-lock.yaml`
